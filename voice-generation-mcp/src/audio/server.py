import contextlib
import json

import mcp.server.stdio
import mcp.types as types
import uvicorn
from mcp.server import NotificationOptions, Server
from mcp.server.models import InitializationOptions
from mcp.server.sse import SseServerTransport
from mcp.server.streamable_http_manager import StreamableHTTPSessionManager
from pydantic import AnyUrl
from starlette.applications import Starlette
from starlette.middleware import Middleware
from starlette.middleware.cors import CORSMiddleware
from starlette.routing import Route

from audio.constants.constant import Descriptions
from audio.constants.enum import Tools
from audio.constants.schema import FetchGenerateAudio, GenerateAudio
from audio.helper.config import HOST, PORT

# Import logger
from audio.loggers.logger import logger
from audio.services.audio import generate_audio, get_updated_audio

from .services.voice import create_voice, get_voice_audios, split_long_string

# from agent import assistant_run
# Store notes as a simple key-value dict to demonstrate state management
notes: dict[str, str] = {}

server = Server("audio")


# await(assistant_run())
def create_voices(text: str, voice_id: str) -> str:
    scripts = split_long_string(text)
    audio_ids = []
    for index, script in enumerate(scripts):
        try:
            audio_id = create_voice(script, voice_id)
            audio_ids.append(audio_id)
        except Exception as e:
            logger.error(f"Error creating voice for script {index}: {str(e)}")
            raise
    # return get_voice_audios(audio_ids) #get_voice_audios
    return str(get_voice_audios(audio_ids))


@server.list_resources()
async def handle_list_resources() -> list[types.Resource]:
    """
    List available note resources.
    Each note is exposed as a resource with a custom note:// URI scheme.
    """
    return [
        types.Resource(
            uri=AnyUrl(f"note://internal/{name}"),
            name=f"Note: {name}",
            description=f"A simple note named {name}",
            mimeType="text/plain",
        )
        for name in notes
    ]


@server.read_resource()
async def handle_read_resource(uri: AnyUrl) -> str:
    """
    Read a specific note's content by its URI.
    The note name is extracted from the URI host component.
    """
    if uri.scheme != "note":
        raise ValueError(f"Unsupported URI scheme: {uri.scheme}")

    name = uri.path
    if name is not None:
        name = name.lstrip("/")
        return notes[name]
    raise ValueError(f"Note not found: {name}")


@server.list_prompts()
async def handle_list_prompts() -> list[types.Prompt]:
    """
    List available prompts.
    Each prompt can have optional arguments to customize its behavior.
    """
    return [
        types.Prompt(
            name="summarize-notes",
            description="Creates a summary of all notes",
            arguments=[
                types.PromptArgument(
                    name="style",
                    description="Style of the summary (brief/detailed)",
                    required=False,
                )
            ],
        )
    ]


@server.get_prompt()
async def handle_get_prompt(
    name: str, arguments: dict[str, str] | None
) -> types.GetPromptResult:
    """
    Generate a prompt by combining arguments with server state.
    The prompt includes all current notes and can be customized via arguments.
    """
    if name != "summarize-notes":
        raise ValueError(f"Unknown prompt: {name}")

    style = (arguments or {}).get("style", "brief")
    detail_prompt = " Give extensive details." if style == "detailed" else ""

    return types.GetPromptResult(
        description="Summarize the current notes",
        messages=[
            types.PromptMessage(
                role="user",
                content=types.TextContent(
                    type="text",
                    text=f"Here are the current notes to summarize:{detail_prompt}\n\n"
                    + "\n".join(
                        f"- {name}: {content}" for name, content in notes.items()
                    ),
                ),
            )
        ],
    )


@server.list_tools()
async def handle_list_tools() -> list[types.Tool]:
    """
    List available tools.
    Each tool specifies its arguments using JSON Schema validation.
    """
    return [
        types.Tool(
            name="create_voices",
            description="Add",
            inputSchema={
                "type": "object",
                "properties": {
                    "text": {"type": "string"},
                    "voice_id": {"type": "string"},
                },
                "required": ["text", "voice_id"],
            },
        ),
        types.Tool(
            name=Tools.GENERATE_AUDIO,
            description=Descriptions.GENERATE_AUDIO,
            inputSchema=GenerateAudio.schema(),
        ),
        types.Tool(
            name=Tools.FETCH_AUDIO,
            description=Descriptions.FETCH_AUDIO,
            inputSchema=FetchGenerateAudio.schema(),
        ),
    ]


@server.call_tool()
async def handle_call_tool(name: str, arguments: dict | None) -> types.CallToolResult:
    """
    Handle tool execution requests.
    Tools can modify server state and notify clients of changes.
    """
    print("name: ", name)
    print("arguments: ", arguments)
    try:
        match name:
            case Tools.GENERATE_VOICE:
                text = arguments.get("text")
                voice_id = arguments.get("voice_id")
                result = create_voices(text=text, voice_id=voice_id)
            case Tools.GENERATE_AUDIO:
                script = arguments.get("script")
                voice_id = arguments.get("voice_id")
                provider = arguments.get("provider")
                result = await generate_audio(
                    script, voice_id=voice_id, provider=provider
                )
                # result = {
                #     "audio_ids": [
                #         "ah04jVk5xWjQ7Alof0",
                #         "YdKFwdw6PmoIrQnRzz",
                #         "M2886dfyeJFCIs0uZS",
                #         "tsbdhOzUdtrUsWTxJc",
                #         "sLOtZFTFgsfTY8hQIy",
                #         "2MjX8BuZghEqgQVBmK",
                #         "gtmOMAfpNYR2Owj5d6",
                #         "mmfTszfwni17dUq6iq",
                #     ],
                #     "voice_id": "s3://voice-cloning-zero-shot/b41d1a8c-2c99-4403-8262-5808bc67c3e0/bentonsaad/manifest.json",
                # }
            case Tools.FETCH_AUDIO:
                audio_ids = arguments.get("audio_ids")
                provider = arguments.get("provider")
                result = await get_updated_audio(audio_ids, provider)
            case _:
                return [types.TextContent(type="text", text=f"Unknown tool: {name}")]
    except Exception as error:
        print("Error:", error)
        error = {"message": f"{str(error)}", "is_error": True}
        return [types.TextContent(type="text", text=json.dumps(error, indent=2))]

    print("result: ", result)
    return [types.TextContent(type="text", text=json.dumps(result, indent=2))]


async def main():
    # Run the server using stdin/stdout streams
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="audio",
                server_version="0.1.0",
                capabilities=server.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={},
                ),
            ),
        )


def create_app():

    sse = SseServerTransport("/audio")

    # # Create an event store for resumability
    # event_store = InMemoryEventStore(max_events_per_stream=100)

    # Create the session manager with the event store
    try:
        # Try with auth parameters (newer MCP versions)
        session_manager = StreamableHTTPSessionManager(
            app=server,
            # event_store=event_store,  # Use our event store for resumability
            json_response=False,  # Use SSE format for responses
            stateless=False,  # Stateful mode for better user experience
            # auth_server_provider=auth_provider,
            # auth_settings=auth_settings,
        )
        logger.info(
            "StreamableHTTPSessionManager initialized with authentication support"
        )
    except TypeError:
        # Fallback for older MCP versions that don't support auth
        logger.warning(
            "Your MCP version doesn't support authentication in StreamableHTTPSessionManager"
        )
        logger.warning(
            "Initializing StreamableHTTPSessionManager without authentication"
        )

        # Try with just the basic parameters
        try:
            session_manager = StreamableHTTPSessionManager(
                app=server,
                # event_store=event_store,
                json_response=False,
            )
            logger.info(
                "StreamableHTTPSessionManager initialized without authentication"
            )
        except TypeError:
            # If that still fails, try with minimal parameters
            logger.warning(
                "Falling back to minimal StreamableHTTPSessionManager initialization"
            )
            session_manager = StreamableHTTPSessionManager(app=server)
    except Exception as e:
        logger.error(f"Failed to initialize StreamableHTTPSessionManager: {e}")
        session_manager = None

    class HandleSSE:
        def __init__(self, sse):
            self.sse = sse

        async def __call__(self, scope, receive, send):
            async with self.sse.connect_sse(scope, receive, send) as streams:
                await server.run(
                    streams[0],
                    streams[1],
                    server.create_initialization_options(),
                )

    class HandleMessages:
        def __init__(self, sse):
            self.sse = sse

        async def __call__(self, scope, receive, send):
            await self.sse.handle_post_message(scope, receive, send)

    class HandleStreamableHttp:
        def __init__(self, session_manager):
            self.session_manager = session_manager

        async def __call__(self, scope, receive, send):
            if self.session_manager is not None:
                try:
                    logger.info("Handling Streamable HTTP connection ....")
                    await self.session_manager.handle_request(scope, receive, send)
                    logger.info("Streamable HTTP connection closed ....")
                except Exception as e:
                    logger.error(f"Error handling Streamable HTTP request: {e}")
            else:
                # Return a 501 Not Implemented response if streamable HTTP is not available
                await send(
                    {
                        "type": "http.response.start",
                        "status": 501,
                        "headers": [(b"content-type", b"application/json")],
                    }
                )
                await send(
                    {
                        "type": "http.response.body",
                        "body": json.dumps(
                            {"error": "Streamable HTTP transport is not available"}
                        ).encode("utf-8"),
                    }
                )

    routes = [
        Route("/sse", endpoint=HandleSSE(sse), methods=["GET"]),
        Route("/audio", endpoint=HandleMessages(sse), methods=["POST"]),
    ]

    # Add Streamable HTTP route if available
    if session_manager is not None:
        routes.append(
            Route(
                "/mcp", endpoint=HandleStreamableHttp(session_manager), methods=["POST"]
            )
        )

    middleware = [
        Middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_methods=["*"],
            allow_headers=["*"],
        )
    ]

    # Define lifespan for session manager
    @contextlib.asynccontextmanager
    async def lifespan(app):
        """Context manager for session manager."""
        if session_manager is not None:
            async with session_manager.run():
                logger.info("Application started with StreamableHTTP session manager!")
                try:
                    yield
                finally:
                    logger.info("Application shutting down...")
        else:
            # No session manager, just yield
            yield

    return Starlette(routes=routes, middleware=middleware, lifespan=lifespan)


def start_server():
    app = create_app()
    logger.info(f"Starting server at {HOST}:{PORT}")
    uvicorn.run(app, host=HOST, port=PORT, timeout_keep_alive=120)


if __name__ == "__main__":
    while True:
        try:
            start_server()
        except KeyboardInterrupt:
            logger.info("Server stopped by user")
            break
        except Exception as e:
            logger.error(f"Server crashed with error: {e}")
            continue
