import logging
import os
import re

# Default log level if not specified or invalid
DEFAULT_LOG_LEVEL = logging.ERROR

# Map log level names to their corresponding logging constants
LOG_LEVEL_MAPPING = {
    "DEBUG": logging.DEBUG,
    "INFO": logging.INFO,
    "WARNING": logging.WARNING,
    "ERROR": logging.ERROR,
    "CRITICAL": logging.CRITICAL,
}

# Fields to be masked in logs
SENSITIVE_FIELDS = ["api_key", "password", "token", "key", "secret"]

# Configure logging
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s [%(filename)s:%(lineno)s - %(funcName)s ] - %(message)s",
    level=logging.INFO,
)
logger = logging.getLogger(__name__)


def get_log_level() -> int:
    """Determine the log level based on environment variables."""
    env_level = os.getenv("LOG_LEVEL", "").upper()
    if os.getenv("DEBUG") == "true":
        return logging.DEBUG
    return LOG_LEVEL_MAPPING.get(env_level, DEFAULT_LOG_LEVEL)


def setup_logger(log_file: str = "debug.log") -> logging.Logger:
    """Set up and configure the logger."""
    log_format = "%(asctime)s - %(name)s - %(levelname)s [%(filename)s:%(lineno)s - %(funcName)s ] - %(message)s"
    formatter = logging.Formatter(log_format)

    # Create a log handler for file output
    file_handler = logging.FileHandler(
        filename=os.path.join(os.path.dirname(__file__), log_file),
        mode="a",
        encoding="utf-8",
    )
    file_handler.setFormatter(formatter)
    file_handler.addFilter(filter_sensitive_fields)

    # Create a log handler for console output
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.addFilter(filter_sensitive_fields)

    # Create a logger and add the handlers
    logger = logging.getLogger()
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    logger.setLevel(get_log_level())

    # Set other loggers to a higher level to reduce verbosity
    for lib in [
        "pymongo",
        "pymongo.topology",
        "openai._base_client",
        "httpcore.http11",
        "httpcore.connection",
        "httpx",
        "requests",
        "urllib3",
        "pexels",
    ]:
        logging.getLogger(lib).setLevel(logging.ERROR)

    return logger


def filter_sensitive_fields(record: logging.LogRecord) -> bool:
    """Filter sensitive fields from log records."""
    if isinstance(record.args, dict):
        record.args = {
            k: ("*****" if any(field in k.lower() for field in SENSITIVE_FIELDS) else v)
            for k, v in record.args.items()
        }
    elif isinstance(record.args, tuple):
        record.args = tuple(
            (
                "*****"
                if any(field in str(arg).lower() for field in SENSITIVE_FIELDS)
                else arg
            )
            for arg in record.args
        )

    if isinstance(record.msg, str):
        record.msg = re.sub(r"\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])", "", record.msg)

    return True


logger = setup_logger()
