import base64
import hashlib
import hmac
import json
import mimetypes
import os
import re
import secrets
import shutil
import sys
import time
import uuid
from datetime import datetime
from urllib.parse import parse_qs, unquote, urlparse

import requests
from image_generation_mcp.loggers.logger import logger

directory = "./content"

if not os.path.exists(directory):
    os.makedirs(directory)


def is_valid_url(url):
    # Regular expression pattern for URL validation
    pattern = re.compile(
        r"^(?:http|ftp)s?://"  # http:// or https:// or ftp:// or ftps://
        r"(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+(?:[A-Z]{2,6}\.?|[A-Z0-9-]{2,}\.?)|"  # domain...
        r"localhost|"  # localhost...
        r"\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})"  # ...or ip
        r"(?::\d+)?"  # optional port
        r"(?:/?|[/?]\S+)$",
        re.IGNORECASE,
    )

    return bool(pattern.match(url))


def save_video(video_url: str, video_id=uuid.uuid4(), directory: str = "./temp") -> str:
    """
    Saves a video from a given URL and returns the path to the video.
    If the file already exists, it doesn't rewrite it.

    Args:
        video_url (str): The URL of the video to save.
        video_id (uuid.UUID): Unique identifier for the video file. Defaults to a new UUID.
        directory (str): The path of the temporary directory to save the video to.

    Returns:
        str: The path to the saved video.
    """
    video_path = f"{directory}/{video_id}.mp4"

    if not os.path.exists(video_path):
        # Ensure the directory exists
        os.makedirs(directory, exist_ok=True)

        # Download and save the video
        response = requests.get(video_url)
        response.raise_for_status()  # Raise an exception for bad responses

        with open(video_path, "wb") as f:
            f.write(response.content)
        print(f"Video saved to {video_path}")
    else:
        print(f"Video already exists at {video_path}")

    return video_path


def clean_dir(path: str) -> None:
    """
    Removes every file and subdirectory in a directory, then removes the directory itself.

    Args:
        path (str): Path to directory.

    Returns:
        None
    """
    try:
        # Check if directory exists
        if not os.path.exists(path):
            logger.warning(f"Directory {path} does not exist")
            return

        # Remove entire directory tree
        shutil.rmtree(path)
        logger.info(f"Removed directory and all contents: {path}")

        logger.info(f"Completely removed {path} directory", "green")
    except Exception as e:
        logger.error(f"Error occurred while removing directory {path}: {str(e)}")


def create_dir(path: str) -> None:
    try:
        if not os.path.exists(path):
            os.mkdir(path)
            logger.info(f"Created directory: {path}")
    except Exception as e:
        logger.error(f"Error occurred while creating directory {path}: {str(e)}")


def generate_numeric_otp():
    # Generate a random integer between 0 and 999999
    otp = secrets.randbelow(1000000)
    # Convert to string and pad with leading zeros if necessary
    return f"{otp:06d}"


def is_valid_url(url):
    """
    Check if the given string is a valid URL.
    """
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except ValueError:
        return False


def save_data_url(data_url, filename):
    # Extract the base64-encoded data and MIME type from the Data URL
    _, data = data_url.split(",", 1)
    mime_type = data_url.split(";")[0].split(":")[1]

    # Determine the file extension based on the MIME type
    if mime_type == "image/gif":
        extension = ".gif"
    elif mime_type == "image/jpeg":
        extension = ".jpg"
    elif mime_type == "image/png":
        extension = ".png"
    else:
        extension = ".dat"

    # Decode the base64 data
    decoded_data = base64.b64decode(unquote(data))

    # Save the decoded data to an image file
    filename = filename + extension
    with open(filename, "wb") as f:
        f.write(decoded_data)


def generate_hmac(secret_key, expires):

    # url info
    resource = "/api/v2/videos/search"

    hmacBuilder = hmac.new(
        bytearray(secret_key + expires, "utf-8"),
        resource.encode("utf-8"),
        hashlib.sha256,
    )
    hmacHex = hmacBuilder.hexdigest()

    return hmacHex


def download_image(image_url, save_path):
    try:
        if not os.path.exists(save_path):
            # Download the image
            response = requests.get(image_url)
            response.raise_for_status()  # Check if the request was successful
            # Save the image to the specified path
            with open(save_path, "wb") as file:
                file.write(response.content)
        return save_path
    except requests.exceptions.RequestException as e:
        print(f"Failed to download the image: {e}")
    except OSError as e:
        print(f"Failed to delete the image: {e}")


def remove_file(save_path):
    # Delete the image file after use
    os.remove(save_path)
    print(f"file {save_path} deleted after use")


def check_link_validity(url):
    try:
        response = requests.get(url)
        response.raise_for_status()
        return response.status_code == 200
    except requests.RequestException:
        return False


def check_env_vars() -> None:
    """
    Checks if the necessary environment variables are set.

    Returns:
        None

    Raises:
        SystemExit: If any required environment variables are missing.
    """
    try:
        required_vars = ["PEXELS_API_KEY", "IMAGEMAGICK_BINARY"]
        missing_vars = [
            var + os.getenv(var)
            for var in required_vars
            if os.getenv(var) is None or (len(os.getenv(var)) == 0)
        ]

        if missing_vars:
            missing_vars_str = ", ".join(missing_vars)
            logger.error(
                f"The following environment variables are missing: {missing_vars_str}",
            )
            logger.error(
                "Please consult 'EnvironmentVariables.md' for instructions on how to set them.",
            )
            sys.exit(1)  # Aborts the program
    except Exception as e:
        logger.error(f"Error occurred while checking environment variables: {str(e)}")
        sys.exit(1)  # Aborts the program if an unexpected error occurs


def check_valid_url(url: str) -> bool:
    """
    Check if the given string is a valid URL.

    Args:
    url (str): The URL to validate.

    Returns:
    bool: True if the URL is valid, False otherwise.
    """

    # Regular expression pattern for URL validation
    pattern = re.compile(
        r"^(?:http|ftp)s?://"  # http:// or https://
        r"(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+(?:[A-Z]{2,6}\.?|[A-Z0-9-]{2,}\.?)|"  # domain...
        r"localhost|"  # localhost...
        r"\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})"  # ...or ip
        r"(?::\d+)?"  # optional port
        r"(?:/?|[/?]\S+)$",
        re.IGNORECASE,
    )

    # Check if the URL matches the pattern
    if re.match(pattern, url) is not None:
        try:
            # Additional check using urlparse
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except ValueError:
            return False
    return False


def download_font(url, output_dir="./temp/font"):
    # Create the output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Get the filename from the URL
    filename = os.path.basename(urlparse(url).path)

    # If the filename doesn't end with .ttf, add it
    if not filename.lower().endswith(".ttf"):
        filename += ".ttf"

    # Full path for the output file
    output_path = os.path.join(output_dir, filename)

    # Download the file
    response = requests.get(url)

    if response.status_code == 200:
        with open(output_path, "wb") as f:
            f.write(response.content)
        print(f"Font downloaded successfully: {output_path}")
        return output_path
    else:
        print(f"Failed to download font. Status code: {response.status_code}")
        return None


def to_stringify(data):
    # Convert sets to lists
    if isinstance(data, set):
        data = list(data)
    return json.dumps(data)


def unlink_folder(folder_path):
    """Delete the specified folder and all its contents."""
    if os.path.exists(folder_path):
        try:
            shutil.rmtree(folder_path)  # Delete the folder and its contents
            print(f"Successfully deleted the folder: {folder_path}")
        except Exception as e:
            print(f"Error deleting the folder: {e}")
    else:
        print(f"The folder does not exist: {folder_path}"),


def split_long_string(input_string):
    if len(input_string) <= 2000:
        return [input_string]
    else:
        substrings = []
        start = 0
        end = 2000
        while start < len(input_string):
            substrings.append(input_string[start:end])
            start = end
            end += 2000
            if end > len(input_string):
                end = len(input_string)
        return substrings


def get_date_month_year():
    # Get the current date and time
    now = datetime.now()

    # Extract year, month, and day
    current_year = now.year
    current_month = now.month
    current_day = now.day
    return {"year": current_year, "month": current_month, "day": current_day}


def str_replace(text, str_to_find, str_to_replace):
    return text.replace(str_to_find, str_to_replace)


def create_directory(directory):
    return os.makedirs(directory, exist_ok=True)


def format_duration(seconds):
    return round(seconds, 2)


def check_url_expiration(url):
    """
    Check if URL has expired by comparing Unix timestamps

    Args:
        url (str): URL containing Expires parameter

    Returns:
        bool: True if expired, False if still valid
    """
    try:
        # Parse URL and get query parameters
        parsed_url = urlparse(url)
        query_params = parse_qs(parsed_url.query)

        # Get expiration timestamp
        expiry_time = int(query_params["Expires"][0])
        current_time = int(time.time())

        # Return True if expired, False if still valid
        return current_time > expiry_time

    except Exception as e:
        print(f"Error checking URL expiration: {str(e)}")
        return None


def verify_token_expiration(expiration_time):
    current_time = int(time.time())
    buffer_time = 5 * 60  # 5 minutes in seconds
    return True if current_time >= (expiration_time - buffer_time) else False


def store_prompt(prompts, type="image"):
    """
    Store the script and story title points in a JSON file.
    Args:
        script (str): The script content.
        title_points (list): A list of title points.
        filename (str): The name of the JSON file to store the data.
    """
    data = {"data": json.loads(prompts)}
    file_name = f"{type}_prompts.json"
    file_path = os.path.join(directory, file_name)
    with open(file_path, "w") as json_file:
        json.dump(data, json_file)


def get_mime_type(url):
    # Try to get MIME type from file extension
    mime_type, _ = mimetypes.guess_type(url)
    if mime_type:
        return mime_type
    # Fallback: Fetch headers for Content-Type
    try:
        response = requests.head(url, allow_redirects=True)
        content_type = response.headers.get("Content-Type")
        if content_type:
            return content_type.split(";")[0]  # Remove encoding info if present
    except requests.RequestException:
        pass
    return "unknown/unknown"  # Default if no MIME type is found


def store_image_data(data):
    """
    Store the script and story title points in a JSON file.
    Args:
        script (str): The script content.
        title_points (list): A list of title points.
        filename (str): The name of the JSON file to store the data.
    """
    data = {data: data}
    file_name = "images.json"
    file_path = os.path.join(directory, file_name)
    with open(file_path, "w") as json_file:
        json.dump(data, json_file)

    return "success"
