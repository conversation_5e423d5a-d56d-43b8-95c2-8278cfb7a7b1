import contextlib
import json

import mcp.server.stdio
import mcp.types as types
import uvicorn
from dotenv import load_dotenv
from mcp.server import NotificationOptions, Server
from mcp.server.models import InitializationOptions
from mcp.server.sse import SseServerTransport
from mcp.server.streamable_http_manager import StreamableHTTPSessionManager
from starlette.applications import Starlette
from starlette.middleware import Middleware
from starlette.middleware.cors import CORSMiddleware
from starlette.routing import Route

from .constants.constant import Descriptions
from .constants.enum import Tools
from .constants.schema import GenerateAIStockImage, GenerateImage
from .helper.config import HOST, PORT
from .helper.s3_manager import S3Uploader
from .loggers.logger import logger
from .services.workflow import (
    generate_ai_image_data,
    generate_image,
    generate_image_data,
)

# import os
s3_uploader = S3Uploader()
load_dotenv()

# Store notes as a simple key-value dict to demonstrate state management
notes: dict[str, str] = {}

server = Server("image-generation-mcp")


@server.list_tools()
async def handle_list_tools() -> list[types.Tool]:
    """
    List available tools.
    Each tool specifies its arguments using JSON Schema validation.
    """
    return [
        # types.Tool(
        #     name=Tools.GENERATE_STOCK_IMAGE,
        #     description=Descriptions.GENERATE_STOCK_IMAGE,
        #     inputSchema=GenerateStockImage.schema(),
        # ),
        types.Tool(
            name=Tools.GENERATE_AI_STOCK_IMAGE,
            description=Descriptions.GENERATE_STOCK_IMAGE,
            inputSchema=GenerateAIStockImage.schema(),
        ),
        types.Tool(
            name=Tools.GENERATE_IMAGE,
            description=Descriptions.GENERATE_IMAGE,
            inputSchema=GenerateImage.schema(),
        ),
        # types.Tool(
        #     name=Tools.FETCH_STOCK_IMAGES,
        #     description=Descriptions.FETCH_STOCK_IMAGES,
        #     inputSchema=GenerateAIStockImage.schema(),
        # ),
    ]


@server.call_tool()
async def handle_call_tool(name: str, arguments: dict | None) -> types.CallToolResult:
    """
    Handle tool execution requests.
    Tools can modify server state and notify clients of changes.
    """

    try:

        print("tool call request: ", name, arguments)

        match name:
            case Tools.GENERATE_AI_STOCK_IMAGE:

                script = arguments.get("script")
                view_type = arguments.get("view_type")

                response = await generate_ai_image_data(script, view_type)

                result = {"stock_image_clips": response}

                # result = {
                #     "stock_image_clips": [
                #         {
                #             "prompts": "A wide shot of a lush rainforest transitioning to a scene of smoldering fires within the jungle, endangered animals like elephants and tigers looking towards the camera, dramatic lighting with a smoky atmosphere. 8k UHD, C4D, Blender, OctaneRender, hyper-realistic, cinematic wildlife photography, highlighting crisis urgency.",
                #             "at_time": 0.0,
                #             "description": "Introduction to the wildlife crisis with compelling imagery.",
                #             "url": "https://ciny-dev.s3.amazonaws.com/ciny-dev/ai_images/23a82fde-4b6c-4ea0-8566-86b4d93818d8.jpg",
                #             "mimetype": "image/jpeg",
                #         },
                #         {
                #             "prompts": "A close-up shot of deforestation, machinery cutting tree logs, transitioning to barren land, with an animated infographic showing the timeline and decline rate of wildlife. Natural lighting with a somber mood. 8k UHD, C4D, Blender, OctaneRender, hyper-realistic, cinematic environmental photography.",
                #             "at_time": 3.424,
                #             "description": "Visual representation of hidden challenges and data on wildlife decline.",
                #             "url": "https://ciny-dev.s3.amazonaws.com/ciny-dev/ai_images/e4ca303d-91ba-4c94-98b7-58b8bd5e4016.jpg",
                #             "mimetype": "image/jpeg",
                #         },
                #         {
                #             "prompts": "A stop-motion animation of library shelves emptying progressively, each shelf marked by years, symbolizing the 68% decline in wildlife population. Soft lighting creating a nostalgic yet alarming atmosphere. 8k UHD, C4D, Blender, OctaneRender, hyper-realistic, cinematic animation.",
                #             "at_time": 10.6,
                #             "description": "Symbolic representation of wildlife population decline.",
                #             "url": "https://ciny-dev.s3.amazonaws.com/ciny-dev/ai_images/d406d5af-da95-4439-b25a-746d3c08afa4.jpg",
                #             "mimetype": "image/jpeg",
                #         },
                #         {
                #             "prompts": "A time-lapse shot of a once-bustling coral reef fading away, or a forest dissolving into a cityscape, capturing ecological change. Natural lighting with a dramatic transformation. 8k UHD, C4D, Blender, OctaneRender, hyper-realistic, cinematic environmental photography.",
                #             "at_time": 28.68,
                #             "description": "Dramatic visualization of ecological and wildlife change.",
                #             "url": "https://ciny-dev.s3.amazonaws.com/ciny-dev/ai_images/0fb3c059-90f3-4a52-80c5-b71e560191e4.jpg",
                #             "mimetype": "image/jpeg",
                #         },
                #         {
                #             "prompts": "Footage of a drone flying over a wildlife sanctuary, researchers analyzing data on screens, community cleanup drives, vibrant community faces, symbolizing innovation and grassroots involvement. Bright natural lighting with an optimistic atmosphere. 8k UHD, C4D, Blender, OctaneRender, hyper-realistic, cinematic conservation photography.",
                #             "at_time": 42.248,
                #             "description": "Illustration of innovative solutions and community involvement in conservation.",
                #             "url": "https://ciny-dev.s3.amazonaws.com/ciny-dev/ai_images/70e0c29e-e841-4d82-8406-9f9733344a5e.jpg",
                #             "mimetype": "image/jpeg",
                #         },
                #         {
                #             "prompts": "A dynamic transition of social media interactions showing subscribes and likes, replays of enthusiastic event attendees raising hands, concluding with a click on a 'subscribe' button graphical overlay. Bright lighting with an engaging atmosphere. 8k UHD, C4D, Blender, OctaneRender, hyper-realistic, cinematic social media interaction.",
                #             "at_time": 68.332,
                #             "description": "Visual appeal to the action of subscribing and engaging with conservation efforts.",
                #             "url": "https://ciny-dev.s3.amazonaws.com/ciny-dev/ai_images/ad919212-2886-4c94-9624-bf2d0c216bd2.jpg",
                #             "mimetype": "image/jpeg",
                #         },
                #         {
                #             "prompts": "Heartwarming clips of siblings releasing baby turtles into the ocean, successfully rehabilitated animals like birds flying off, communities celebrating conservation milestones. Warm natural lighting with an uplifting atmosphere. 8k UHD, C4D, Blender, OctaneRender, hyper-realistic, cinematic wildlife conservation photography.",
                #             "at_time": 75.556,
                #             "description": "Inspiration to join conservation efforts with positive imagery.",
                #             "url": "https://ciny-dev.s3.amazonaws.com/ciny-dev/ai_images/2de08761-2ffc-49ee-94ad-33a24ccbb15e.jpg",
                #             "mimetype": "image/jpeg",
                #         },
                #     ],
                # }

                print("result", result)
            case Tools.GENERATE_STOCK_IMAGE:

                script = arguments.get("script")
                view_type = arguments.get("view_type")

                response = await generate_image_data(script, view_type)

                result = {"stock_image_clips": response}

                print("result", result)

            case Tools.GENERATE_IMAGE:

                prompt = arguments.get("prompt")
                view_type = arguments.get("view_type")

                response = generate_image(prompt, view_type)

                result = {"data": response}

                print("result", result)

            case Tools.FETCH_STOCK_IMAGES:

                script = arguments.get("script")
                view_type = arguments.get("view_type")

                # response = await generate_ai_image_data(script, view_type)

                # result = {"stock_image_clips": response}

                print("result", result)
            case _:
                return [types.TextContent(type="text", text=f"Unknown tool: {name}")]

    except Exception as error:
        print("Error:", str(error))
        error = {"message": str(error), "is_error": True}
        return [types.TextContent(type="text", text=json.dumps(error, indent=2))]

    return [types.TextContent(type="text", text=json.dumps(result, indent=2))]


async def main():
    # Run the server using stdin/stdout streams
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="image-generation-mcp",
                server_version="0.1.0",
                capabilities=server.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={},
                ),
            ),
        )


def create_app():

    sse = SseServerTransport("/stock-image")

    # Create the session manager with the event store
    try:
        # Try with auth parameters (newer MCP versions)
        session_manager = StreamableHTTPSessionManager(
            app=server,
            # event_store=event_store,  # Use our event store for resumability
            json_response=False,  # Use SSE format for responses
            stateless=False,  # Stateful mode for better user experience
            # auth_server_provider=auth_provider,
            # auth_settings=auth_settings,
        )
        logger.info(
            "StreamableHTTPSessionManager initialized with authentication support"
        )
    except TypeError:
        # Fallback for older MCP versions that don't support auth
        logger.warning(
            "Your MCP version doesn't support authentication in StreamableHTTPSessionManager"
        )
        logger.warning(
            "Initializing StreamableHTTPSessionManager without authentication"
        )

        # Try with just the basic parameters
        try:
            session_manager = StreamableHTTPSessionManager(
                app=server,
                # event_store=event_store,
                json_response=False,
            )
            logger.info(
                "StreamableHTTPSessionManager initialized without authentication"
            )
        except TypeError:
            # If that still fails, try with minimal parameters
            logger.warning(
                "Falling back to minimal StreamableHTTPSessionManager initialization"
            )
            session_manager = StreamableHTTPSessionManager(app=server)
    except Exception as e:
        logger.error(f"Failed to initialize StreamableHTTPSessionManager: {e}")
        session_manager = None

    class HandleSSE:
        def __init__(self, sse):
            self.sse = sse

        async def __call__(self, scope, receive, send):
            async with self.sse.connect_sse(scope, receive, send) as streams:
                await server.run(
                    streams[0],
                    streams[1],
                    server.create_initialization_options(),
                )

    class HandleMessages:
        def __init__(self, sse):
            self.sse = sse

        async def __call__(self, scope, receive, send):
            await self.sse.handle_post_message(scope, receive, send)

    class HandleStreamableHttp:
        def __init__(self, session_manager):
            self.session_manager = session_manager

        async def __call__(self, scope, receive, send):
            if self.session_manager is not None:
                try:
                    logger.info("Handling Streamable HTTP connection ....")
                    await self.session_manager.handle_request(scope, receive, send)
                    logger.info("Streamable HTTP connection closed ....")
                except Exception as e:
                    logger.error(f"Error handling Streamable HTTP request: {e}")
            else:
                # Return a 501 Not Implemented response if streamable HTTP is not available
                await send(
                    {
                        "type": "http.response.start",
                        "status": 501,
                        "headers": [(b"content-type", b"application/json")],
                    }
                )
                await send(
                    {
                        "type": "http.response.body",
                        "body": json.dumps(
                            {"error": "Streamable HTTP transport is not available"}
                        ).encode("utf-8"),
                    }
                )

    routes = [
        Route("/sse", endpoint=HandleSSE(sse), methods=["GET"]),
        Route("/stock-image", endpoint=HandleMessages(sse), methods=["POST"]),
    ]

    # Add Streamable HTTP route if available
    if session_manager is not None:
        routes.append(
            Route(
                "/mcp", endpoint=HandleStreamableHttp(session_manager), methods=["POST"]
            )
        )

    middleware = [
        Middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_methods=["*"],
            allow_headers=["*"],
        )
    ]

    # Define lifespan for session manager
    @contextlib.asynccontextmanager
    async def lifespan(app):
        """Context manager for session manager."""
        if session_manager is not None:
            async with session_manager.run():
                logger.info("Application started with StreamableHTTP session manager!")
                try:
                    yield
                finally:
                    logger.info("Application shutting down...")
        else:
            # No session manager, just yield
            yield

    return Starlette(routes=routes, middleware=middleware, lifespan=lifespan)


def start_server():
    app = create_app()
    logger.info(f"Starting server at {HOST}:{PORT}")
    uvicorn.run(app, host=HOST, port=PORT, timeout_keep_alive=120)


if __name__ == "__main__":
    while True:
        try:
            start_server()
        except KeyboardInterrupt:
            logger.info("Server stopped by user")
            break
        except Exception as e:
            logger.error(f"Server crashed with error: {e}")
            continue
