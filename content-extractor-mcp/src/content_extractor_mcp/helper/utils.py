import os
import re
import shutil

# Import logger
from content_extractor_mcp.loggers.logger import logger


def create_dir(path: str) -> None:
    try:
        if not os.path.exists(path):
            os.mkdir(path)
    except Exception as e:
        print("Could not create directory", e)
        pass


def is_valid_url(url):
    # Regular expression pattern for URL validation
    pattern = re.compile(
        r"^(?:http|ftp)s?://"  # http:// or https:// or ftp:// or ftps://
        r"(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+(?:[A-Z]{2,6}\.?|[A-Z0-9-]{2,}\.?)|"  # domain...
        r"localhost|"  # localhost...
        r"\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})"  # ...or ip
        r"(?::\d+)?"  # optional port
        r"(?:/?|[/?]\S+)$",
        re.IGNORECASE,
    )

    return bool(pattern.match(url))


def clean_dir(path: str) -> None:
    """
    Removes every file and subdirectory in a directory, then removes the directory itself.

    Args:
        path (str): Path to directory.

    Returns:
        None
    """
    try:
        # Check if directory exists
        if not os.path.exists(path):
            logger.warning(f"Directory {path} does not exist")
            return

        # Remove entire directory tree
        shutil.rmtree(path)
        logger.info(f"Removed directory and all contents: {path}")

        logger.info(f"Completely removed {path} directory")
    except Exception as e:
        logger.error(f"Error occurred while removing directory {path}: {str(e)}")
