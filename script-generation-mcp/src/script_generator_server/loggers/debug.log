2025-06-21 11:55:06,245 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,264 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,283 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,303 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,323 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,342 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,361 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,380 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,398 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,417 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,437 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,470 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,496 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,520 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,541 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,600 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,618 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,639 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,657 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,675 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,694 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,713 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,731 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,749 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,767 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,785 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,803 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,821 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,838 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,856 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,875 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,893 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,911 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,928 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,946 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,963 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:06,982 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:07,000 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:07,018 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:07,039 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:07,059 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:07,077 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:07,095 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:07,112 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:07,130 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:07,148 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:07,166 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:07,183 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:07,201 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:07,218 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:07,236 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:07,254 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:07,271 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:07,289 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:07,306 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:07,324 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:07,342 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:07,361 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:07,379 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
2025-06-21 11:55:07,397 - autogen.oai.client - WARNING [client.py:129 - __init__ ] - The API key specified is not a valid OpenAI format; it won't work with the OpenAI-hosted model.
