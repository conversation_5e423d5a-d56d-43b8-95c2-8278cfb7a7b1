import json
import re
from typing import List, Tuple

# import google.generativeai as genai
# import anthropic
from openai import OpenAI

from .config import REQUESTY_API_KEY
from .model_config import ModelProvider, default_openai_model
from .prompts import build_script_prompt


def generate_response(
    prompt: str,
    provider=ModelProvider.OPENAI,
) -> str:
    """
    Generate a script for a video, depending on the subject of the video.
    Args:
        video_subject (str): The subject of the video.
    Returns:
        str: The response from the AI model.
    """
    try:
        if provider == ModelProvider.OPENAI:
            # Use Requesty.ai with OpenAI-compatible client
            client = OpenAI(
                api_key=REQUESTY_API_KEY, base_url="https://router.requesty.ai/v1"
            )
            response = client.chat.completions.create(
                model=default_openai_model,
                messages=[{"role": "user", "content": prompt}],
            )
            token_used = response.usage.total_tokens
            print("requesty token_used ===>>", token_used)
            response = response.choices[0].message.content
        # if provider == ModelProvider.GOOGLE:
        #     genai.configure(api_key=GEMINI_API_KEY)
        #     model = genai.GenerativeModel(default_gemini_model)
        #     response_model = model.generate_content(prompt)
        #     print(
        #         "google token_used ===>>",
        #         response_model.usage_metadata.total_token_count,
        #     )
        #     response = response_model.text
        # if provider == ModelProvider.DEEPSEEK:
        #     # for backward compatibility, you can still use `https://api.deepseek.com/v1` as `base_url`.
        #     client = OpenAI(api_key=DEEPSEEK_API, base_url="https://api.deepseek.com")
        #     response = client.chat.completions.create(
        #         model=default_deepseek_model,
        #         messages=[
        #             {"role": "system", "content": "You are a helpful assistant"},
        #             {"role": "user", "content": prompt},
        #         ],
        #         max_tokens=1024,
        #         temperature=0.7,
        #         stream=False,
        #     )
        #     response = response.choices[0].message.content
        # if provider == ModelProvider.ANTHROPIC:
        #     client = anthropic.Anthropic(
        #         api_key=CLAUDE_KEY,
        #     )
        #     message = client.messages.create(
        #         model=default_claude_model,
        #         max_tokens=1024,
        #         messages=[{"role": "user", "content": prompt}],
        #     )
        #     response = message.content
        return response
    except Exception as e:
        print(f"[-] Error: openai generate_response {str(e)}")
        raise


def generate_ai_script(
    topic: str,
    video_type,
    keywords,
    custom_prompt=None,
) -> str:
    """
    Generate a script for a video, depending on the subject of the video, the number of paragraphs, and the AI model.

    Args:

        video_subject (str): The subject of the video.

        paragraph_number (int): The number of paragraphs to generate.

    Returns:

        str: The script for the video.

    """

    # Build prompt

    prompt = build_script_prompt(
        topic,
        video_type,
        keywords,
        custom_prompt,
    )

    # Generate script
    response = generate_response(prompt)

    # Return the generated script
    if response:
        # Clean the script
        # Remove asterisks, hashes
        response = response.replace("*", "")
        response = response.replace("#", "")

        # Remove markdown syntax
        response = re.sub(r"\[.*\]", "", response)
        response = re.sub(r"\(.*\)", "", response)

        # Split the script into paragraphs
        paragraphs = response.split("\n\n")

        # Join the selected paragraphs into a single string
        final_script = "\n\n".join(paragraphs)

        # Print to console the number of paragraphs used
        print(f"Number of paragraphs used: {len(paragraphs)}")

        return final_script
    else:
        print("[-] GPT returned an empty response.")
        return None
