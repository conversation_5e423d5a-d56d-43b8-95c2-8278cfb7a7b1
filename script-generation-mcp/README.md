# script-generator-server MCP server

This MCP server automates script generation based on topics and keywords using multiple AI agents.

## Features

- **Streamable HTTP Transport**: Supports the latest MCP transport protocol for improved performance and reliability
- **Authentication**: OAuth2-based authentication for secure access to the server
- **Latest MCP Version**: Updated to use MCP version 1.8.1 with all the latest features
- **Fallback Mechanism**: Gracefully falls back to SSE transport if Streamable HTTP is not available

## Components

### Resources

The server implements a script generation system with:

- Custom script:// URI scheme for accessing individual scripts
- Each script resource has a name, description, and text/plain mimetype

### Prompts

The server provides a single prompt:

- generate-script: Creates scripts based on provided topics and keywords
  - Optional "style" argument to control detail level (brief/detailed)
  - Generates prompt combining all current topics and keywords with style preference

### Tools

The server implements one tool:

- generate_script: Generates and fetches scripts
  - Takes "topic" and "keywords" as required string arguments
  - Updates server state and notifies clients of resource changes

### Authentication

The server implements a robust OAuth-based authentication system:

- **Client Registration**:

  - POST `/oauth/register` - Register a new client and get client credentials
  - Request body: `{"scopes": ["script_generate"]}`
  - Response: `{"client_id": "...", "client_secret": "...", "scopes": [...]}`

- **Token Request**:

  - POST `/oauth/token` - Get an access token using client credentials
  - Request body: `{"client_id": "...", "client_secret": "...", "scopes": [...]}`
  - Response: `{"access_token": "...", "token_type": "bearer", "expires_in": 3600, "scope": "..."}`

- **Token Revocation**:
  - POST `/oauth/revoke` - Revoke an access token
  - Request body: `{"token": "..."}`
  - Response: `{"status": "success"}`

The authentication system is based on the MCP OAuth provider interface, making it compatible with standard OAuth clients while providing a simplified implementation for direct use.

## Transport Endpoints

The server supports multiple transport protocols:

- **SSE Transport**:

  - GET `/sse` - For establishing SSE connections
  - POST `/script` - For sending messages to the server
  - Supports authentication via Bearer token
  - Compatible with different MCP versions

- **Streamable HTTP Transport**:
  - POST `/mcp` - Single endpoint for all Streamable HTTP communication
  - Uses the StreamableHTTPSessionManager for improved performance and reliability
  - Supports resumable connections with an in-memory event store
  - Automatically falls back to SSE if Streamable HTTP is not available
  - Supports authentication via Bearer token
  - Compatible with different MCP versions

## Configuration

### API Configuration

This server uses **Requesty.ai** as the LLM provider instead of OpenAI directly. Requesty.ai provides:

- Access to 160+ models including GPT-4o, Claude, and others
- Unified API gateway with caching, failover, and cost optimization
- OpenAI-compatible API interface

To get started:

1. Sign up at [https://app.requesty.ai/sign-up](https://app.requesty.ai/sign-up)
2. Get your API key from [https://app.requesty.ai/getting-started](https://app.requesty.ai/getting-started)
3. Set the `REQUESTY_API_KEY` environment variable in your `.env` file

### Usage with Claude Desktop

Add this to your `claude_desktop_config.json`:

<details>
<summary>Using uvx</summary>

```json
"mcpServers": {
  "script-generator-server": {
    "command": "uvx",
    "args": ["script-generator-server"]
  }
}
```

</details>

<details>
<summary>Using docker</summary>

- Note: replace '/Users/<USER>' with the a path that you want to be accessible by this tool

```json
"mcpServers": {
  "script-generator-server": {
    "command": "docker",
    "args": ["run", "--rm", "-i", "--mount", "type=bind,src=/Users/<USER>/Users/<USER>", "script-generator-server"]
  }
}
```

</details>

<details>
<summary>Using pip installation</summary>

```json
"mcpServers": {
  "script-generator-server": {
    "command": "python",
    "args": ["-m", "script-generator-server"]
  }
}
```

</details>

### Usage with [Zed](https://github.com/zed-industries/zed)

Add to your Zed settings.json:

<details>
<summary>Using uvx</summary>

```json
"context_servers": [
  "script-generator-server": {
    "command": {
      "path": "uvx",
      "args": ["script-generator-server"]
    }
  }
],
```

</details>

<details>
<summary>Using pip installation</summary>

```json
"context_servers": {
  "script-generator-server": {
    "command": {
      "path": "python",
      "args": ["-m", "script-generator-server"]
    }
  }
},
```

</details>

## Quickstart

### Install

1. Clone the repository:

```bash
git clone https://gitlab.rapidinnovation.tech/mcp-server/*********************.git
cd *********************
```

2. Create a virtual environment:

```bash
python -m venv .venv
source .venv/bin/activate  # On Windows use `.venv\Scripts\activate`
```

3. Create a `.env` file using the `.env.example` file:

```bash
cp .env.example .env
```

4. Run the server using `uv` command:

```bash
uv run script-generator-server
```

This process will install all dependencies.

### Claude Desktop

On MacOS: `~/Library/Application\ Support/Claude/claude_desktop_config.json`
On Windows: `%APPDATA%/Claude/claude_desktop_config.json`

<details>
  <summary>Development/Unpublished Servers Configuration</summary>
  ```json
  "mcpServers": {
    "script-generator-server": {
      "command": "uv",
      "args": [
        "--directory",
        "/Users/<USER>/Desktop/Projects/ciny/ciny-main/mcp-server/script-generator-server",
        "run",
        "script-generator-server"
      ]
    }
  }
  ```
</details>

<details>
  <summary>Published Servers Configuration</summary>
  ```json
  "mcpServers": {
    "script-generator-server": {
      "command": "uvx",
      "args": [
        "script-generator-server"
      ]
    }
  }
  ```
</details>

## Development

### Building and Publishing

To prepare the package for distribution:

1. Sync dependencies and update lockfile:

```bash
uv sync
```

2. Build package distributions:

```bash
uv build
```

This will create source and wheel distributions in the `dist/` directory.

3. Publish to PyPI:

```bash
uv publish
```

Note: You'll need to set PyPI credentials via environment variables or command flags:

- Token: `--token` or `UV_PUBLISH_TOKEN`
- Or username/password: `--username`/`UV_PUBLISH_USERNAME` and `--password`/`UV_PUBLISH_PASSWORD`

### Debugging

You can use the MCP inspector to debug the server. For uvx installations:

```bash
npx @modelcontextprotocol/inspector uvx script-generator-server
```

Or if you've installed the package in a specific directory or are developing on it:

```bash
cd /Users/<USER>/Desktop/Projects/ciny/ciny-main/mcp-server/*********************
npx @modelcontextprotocol/inspector uv run script-generator-server
```

Running `tail -n 20 -f ~/Library/Logs/Claude/mcp*.log` will show the logs from the server and may help you debug any issues.

### Testing

The project includes comprehensive tests for different connection types and authentication:

1. Standard I/O connection
2. SSE connection
3. Streamable HTTP connection
4. Authentication and token management

To run all tests:

```bash
python -m pytest tests/
```

To run specific test files:

```bash
# Test authentication and streaming
python -m pytest tests/test_auth_and_streaming.py

# Test specific connection types
python -m pytest tests/test_connections.py
```

To test a specific connection type using the client:

```bash
# Test Standard I/O connection
python client.py --connection-type stdio

# Test SSE connection
python client.py --connection-type sse

# Test Streamable HTTP connection
python client.py --connection-type streamable-http

# Test with authentication
python client.py --connection-type sse --auth
python client.py --connection-type streamable-http --auth

# Test all connection types
python client.py --connection-type all
```

### Using Docker

#### Build

Docker build:

```bash
docker build -t script-generator-server .
```

#### Run

Docker run:

```bash
docker run -it script-generator-server

docker run -it --env-file .env script-generator-server
```

# Troubleshooting

## Common Issues

1. **StreamableHTTPSessionManager not available**

   - Error: `StreamableHTTPSessionManager is not available in this version of MCP`
   - Solution: Update to MCP 1.8.1 or later, or use SSE transport instead

2. **Authentication errors**

   - Error: `Invalid client credentials` or `Token expired`
   - Solution: Register a new client and get a new token

3. **Connection errors**

   - Error: `Connection refused` or `Connection reset`
   - Solution: Make sure the server is running and accessible

4. **Compatibility issues with different MCP versions**

   - Error: `SseServerTransport.__init__() got an unexpected keyword argument 'auth_server_provider'`
   - Solution: The server automatically detects the available parameters and falls back to compatible initialization methods

5. **Streamable HTTP connection errors**
   - Error: `handle_streamable_http() missing 2 required positional arguments: 'receive' and 'send'`
   - Solution: This has been fixed by using a class-based endpoint handler instead of a function

# License

This MCP server is licensed under the MIT License. This means you are free to use, modify, and distribute the software, subject to the terms and conditions of the MIT License. For more details, please see the LICENSE file in the project repository.
